/**
 * All of the CSS for your admin-specific functionality should be
 * included in this file.
 */
/*
* Start Common CSS for all
*/
.ifaq-container {
    max-width: 850px;
    background: #fff;
    padding: 40px;
    margin: auto;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06);
}

.ifaq-container h1 {
    font-size: 32px;
    margin-bottom: 5px;
    color: #23282d;
}

.ifaq-container p {
    font-size: 15px;
    color: #666;
    margin-bottom: 30px;
}

.ifaq-container h2 {
    font-size: 20px;
    border-bottom: 2px solid #e2e2e2;
    padding-bottom: 8px;
    margin-top: 40px;
    margin-bottom: 20px;
    color: #333;
}
.ifaq-form-group {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding-top:20px;
}

.ifaq-form-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.ifaq-form-row label {
    flex: 1;
    font-weight: 600;
    color: #333;
}

.ifaq-form-row .input-field {
    flex: 2;
    max-width: 400px;
}

textarea,
select,
input
{
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    border-radius: 5px;
    padding: 10px;
    border: 1px solid #ccc;
    font-size: 14px;
    transition: border-color 0.2s ease-in-out;
}

input[type="checkbox"] {
    transform: scale(1.2);
    margin: 5px 5px 5px 0;
}

textarea:focus,
select:focus,
input[type="text"]:focus {
    border-color: #2271b1;
    outline: none;
}
.ifaq_required{
    color:red;
}

.form-actions {
    margin-top: 40px;
    display: flex;
    gap: 15px;
}
.submit {
    margin-top: 20px;
}

.button {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

.button-primary {
    background-color: #007cba;
    color: #fff;
}

.button-primary:hover {
    background-color: #006ba1;
}

.button-secondary {
    background-color: #e2e2e2;
    color: #333;
}

.button-secondary:hover {
    background-color: #d2d2d2;
}

/*
interactive faq lists
 */
.ifaq-accordion {
    background: #f9f9f9;
    border-radius: 10px;
    padding: 15px;
    max-width: 900px;
    margin-top: 20px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.ifaq-accordion-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    background-color: #fff;
    transition: box-shadow 0.3s;
}

.ifaq-accordion-item:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
}

.ifaq-question {
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    color: #23282d;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ifaq-icon {
    transition: transform 0.3s ease;
    font-size: 16px;
    color: #666;
}

.ifaq-question.active .ifaq-icon {
    transform: rotate(180deg);
}

.ifaq-answer {
    display: none;
    padding: 0 20px 15px;
    font-size: 14px;
    color: #444;
    line-height: 1.6;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ifaq-meta {
    margin-top: 10px;
    font-size: 12px;
    color: #777;
}

.ifaq-status.active {
    color: green;
    font-weight: 600;
}

.ifaq-status.deactive {
    color: red;
    font-weight: 600;
}

.ifaq-actions {
    margin-top: 10px;
}

.ifaq-actions a {
    display: inline-block;
    margin-right: 10px;
    font-size: 13px;
    padding: 6px 10px;
    border-radius: 4px;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.ifaq-actions a.edit {
    background-color: #0073aa;
    color: #fff;
}

.ifaq-actions a.edit:hover {
    background-color: #005a87;
}

.ifaq-actions a.delete {
    background-color: #ca4a1f;
    color: #fff;
}

.ifaq-actions a.delete:hover {
    background-color: #a33e1a;
}
#ifaq-pagination ul {
    display: flex;
    flex-direction: row;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 20px 0;
    gap: 10px;
}

#ifaq-pagination ul li a {
    display: block;
    padding: 8px 14px;
    text-decoration: none;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    color: #333;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

#ifaq-pagination ul li a:hover,
#ifaq-pagination ul li a.active {
    background-color: #0073aa;
    color: #fff;
    border-color: #0073aa;
}
/*Ifaq Categories*/
.ifaq-admin-categories{
    display:flex;
    flex-direction:row;
    gap: 30px;
}
.add_category{
    flex:1;
}
.all_categories{
    flex:2;
}
.text-danger {
    color: #dc3545;
}

@media (max-width: 768px) {
    .ifaq-admin-categories {
        flex-direction: column;
    }
}

/*Ifaq Message*/
#ifaq-message {
    padding: 10px 35px 10px 10px;
    border-radius: 4px;
    position: relative;
}

#ifaq-message.success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
    padding: 10px;
    border-radius: 6px;
}

#ifaq-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
    padding: 10px;
    border-radius: 6px;
}

.ifaq-close {
    font-weight: bold;
    font-size: 20px;
    line-height: 20px;
}
